#!/usr/bin/env python3
"""
Тесты для UzumParser
"""

import asyncio
import json
import os
from test import UzumParser


async def test_basic_functionality():
    """Базовый тест функциональности парсера"""
    print("🧪 Запуск базовых тестов...")
    
    parser = UzumParser()
    
    # Тест 1: Проверка инициализации
    assert parser.base_url == "https://uzum.uz"
    assert parser.products == []
    print("✅ Тест инициализации пройден")
    
    # Тест 2: Проверка извлечения веса из текста
    test_texts = [
        "Молоко 1 л свежее",
        "Хлеб 500 г белый",
        "Мука 2.5 кг высший сорт",
        "Сок 250 мл апельсиновый"
    ]
    
    expected_weights = ["1 л", "500 г", "2.5 кг", "250 мл"]
    
    for text, expected in zip(test_texts, expected_weights):
        weight = parser._extract_weight_from_text(text)
        assert weight == expected, f"Ожидался вес {expected}, получен {weight}"
    
    print("✅ Тест извлечения веса пройден")
    
    print("🎉 Все базовые тесты пройдены!")


async def test_html_parsing():
    """Тест парсинга HTML"""
    print("\n🧪 Тест парсинга HTML...")
    
    # Создаём тестовый HTML
    test_html = """
    <html>
    <body>
        <div class="product-card">
            <a href="/product/12345" title="Тестовый товар 1кг">
                <img src="/images/test.jpg" alt="Товар">
                <h3>Тестовый товар 1кг</h3>
            </a>
            <div class="price">15000 сум</div>
            <div class="reviews">25 отзывов</div>
        </div>
        
        <div class="product-card">
            <a href="/product/67890">
                <span>Другой товар 500г</span>
            </a>
            <span class="cost">8500 сум</span>
        </div>
    </body>
    </html>
    """
    
    parser = UzumParser()
    products = parser.parse_products_from_spa(test_html)
    
    print(f"Найдено товаров: {len(products)}")
    
    if products:
        for i, product in enumerate(products):
            print(f"Товар {i+1}:")
            print(f"  Название: {product.get('name', 'Не найдено')}")
            print(f"  Цена: {product.get('price', 'Не найдено')}")
            print(f"  URL: {product.get('url', 'Не найдено')}")
            print(f"  Вес: {product.get('weight', 'Не найдено')}")
            print(f"  Отзывы: {product.get('reviews_count', 'Не найдено')}")
    
    print("✅ Тест парсинга HTML завершён")


async def test_real_website():
    """Тест на реальном сайте (осторожно!)"""
    print("\n🧪 Тест на реальном сайте...")
    print("⚠️  Этот тест может занять время и использовать интернет")
    
    response = input("Запустить тест на реальном сайте? (y/N): ")
    if response.lower() != 'y':
        print("Тест пропущен")
        return
    
    parser = UzumParser()
    
    # Тестируем на простой категории
    test_url = "https://uzum.uz/ru/category/smartfony-15509"
    
    try:
        print(f"Тестируем URL: {test_url}")
        products = await parser.parse_category(test_url)
        
        if products:
            print(f"✅ Успешно получено {len(products)} товаров")
            
            # Показываем первые 3 товара
            for i, product in enumerate(products[:3]):
                print(f"\nТовар {i+1}:")
                print(f"  Название: {product.get('name', 'Не найдено')[:50]}...")
                print(f"  Цена: {product.get('price', 'Не найдено')}")
                print(f"  URL: {product.get('url', 'Не найдено')}")
        else:
            print("❌ Товары не найдены")
            
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")


async def test_error_handling():
    """Тест обработки ошибок"""
    print("\n🧪 Тест обработки ошибок...")
    
    parser = UzumParser()
    
    # Тест с невалидным URL
    try:
        await parser.load_page_with_js("invalid-url")
        print("❌ Должна была быть ошибка для невалидного URL")
    except Exception:
        print("✅ Корректно обработана ошибка невалидного URL")
    
    # Тест с пустым HTML
    products = parser.parse_products_from_spa("")
    assert products == [], "Пустой HTML должен возвращать пустой список"
    print("✅ Корректно обработан пустой HTML")
    
    print("✅ Тесты обработки ошибок пройдены")


async def main():
    """Запуск всех тестов"""
    print("🚀 Запуск тестов UzumParser")
    print("=" * 50)
    
    try:
        await test_basic_functionality()
        await test_html_parsing()
        await test_error_handling()
        await test_real_website()
        
        print("\n" + "=" * 50)
        print("🎉 Все тесты завершены!")
        
    except Exception as e:
        print(f"\n💥 Ошибка в тестах: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())