#!/usr/bin/env python3
"""
Отладочный скрипт для анализа структуры Uzum Market
"""

import asyncio
import re
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup


async def debug_uzum_structure(url: str):
    """Анализирует структуру страницы Uzum"""
    print(f"🔍 Анализ структуры: {url}")
    print("=" * 60)
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # Показываем браузер
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
        
        page = await context.new_page()
        
        try:
            print("📱 Загружаем страницу...")
            response = await page.goto(url, wait_until="domcontentloaded", timeout=30000)
            
            if response:
                print(f"HTTP статус: {response.status}")
            
            # Ждём немного для загрузки JS
            await page.wait_for_timeout(5000)
            
            # Проверяем, что загрузилось
            print("\n🔍 Анализ загруженного контента:")
            
            # Проверяем наличие различных элементов
            elements_to_check = [
                ('a[href*="/product/"]', 'Ссылки на товары'),
                ('img[src*="product"]', 'Изображения товаров'),
                ('[class*="product"]', 'Элементы с классом product'),
                ('[class*="card"]', 'Карточки'),
                ('[class*="grid"]', 'Сетки'),
                ('[data-testid]', 'Элементы с data-testid'),
                ('button', 'Кнопки'),
                ('input', 'Поля ввода'),
            ]
            
            for selector, description in elements_to_check:
                count = await page.evaluate(f"""
                    () => document.querySelectorAll('{selector}').length
                """)
                print(f"  {description}: {count}")
            
            # Получаем все ссылки
            print("\n🔗 Анализ ссылок:")
            links = await page.evaluate("""
                () => {
                    const links = Array.from(document.querySelectorAll('a[href]'));
                    return links.slice(0, 20).map(link => ({
                        href: link.href,
                        text: link.textContent.trim().substring(0, 50)
                    }));
                }
            """)
            
            for i, link in enumerate(links, 1):
                print(f"  {i}. {link['href']} - {link['text']}")
            
            # Проверяем наличие JavaScript ошибок
            print("\n⚠️ Проверка ошибок JavaScript:")
            errors = await page.evaluate("""
                () => {
                    const errors = window.jsErrors || [];
                    return errors.slice(0, 5);
                }
            """)
            
            if errors:
                for error in errors:
                    print(f"  ❌ {error}")
            else:
                print("  ✅ Ошибок не найдено")
            
            # Сохраняем скриншот
            await page.screenshot(path='uzum_debug_screenshot.png')
            print("\n📸 Скриншот сохранён: uzum_debug_screenshot.png")
            
            # Получаем HTML
            html = await page.content()
            
            # Анализируем HTML
            print(f"\n📄 HTML размер: {len(html)} символов")
            
            soup = BeautifulSoup(html, 'html.parser')
            
            # Ищем мета-информацию
            title = soup.find('title')
            if title:
                print(f"📝 Заголовок: {title.get_text()}")
            
            # Проверяем статус код в мета-тегах
            status_meta = soup.find('meta', {'name': 'prerender-status-code'})
            if status_meta:
                print(f"🚨 Статус код в мета: {status_meta.get('content')}")
            
            # Ищем возможные контейнеры товаров
            print("\n🎯 Поиск возможных контейнеров товаров:")
            
            potential_containers = [
                'div[class*="catalog"]',
                'div[class*="product"]',
                'div[class*="grid"]',
                'div[class*="list"]',
                'section',
                'main',
                '[data-testid]'
            ]
            
            for selector in potential_containers:
                elements = soup.select(selector)
                if elements:
                    print(f"  {selector}: {len(elements)} элементов")
                    # Показываем первые несколько классов
                    for elem in elements[:3]:
                        classes = elem.get('class', [])
                        if classes:
                            print(f"    - Классы: {' '.join(classes[:5])}")
            
            # Ждём ещё немного и проверяем снова
            print("\n⏳ Ждём дополнительную загрузку...")
            await page.wait_for_timeout(10000)
            
            # Скроллим
            await page.evaluate("window.scrollTo(0, 1000);")
            await page.wait_for_timeout(3000)
            
            # Проверяем снова
            final_product_count = await page.evaluate("""
                () => document.querySelectorAll('a[href*="/product/"]').length
            """)
            print(f"🔄 Товаров после скролла: {final_product_count}")
            
            # Сохраняем финальный HTML
            final_html = await page.content()
            with open('uzum_final_debug.html', 'w', encoding='utf-8') as f:
                f.write(final_html)
            print("💾 Финальный HTML сохранён: uzum_final_debug.html")
            
        except Exception as e:
            print(f"❌ Ошибка: {e}")
        
        finally:
            await context.close()
            await browser.close()


async def main():
    urls_to_test = [
        "https://uzum.uz/ru",
        "https://uzum.uz/ru/catalog",
        "https://uzum.uz/ru/category/smartfony-15509",
        "https://uzum.uz/ru/search?q=телефон"
    ]
    
    print("🧪 Отладочный анализ Uzum Market")
    print("=" * 60)
    
    for i, url in enumerate(urls_to_test, 1):
        print(f"\n📍 Тест {i}/{len(urls_to_test)}")
        try:
            await debug_uzum_structure(url)
        except Exception as e:
            print(f"❌ Ошибка при тестировании {url}: {e}")
        
        if i < len(urls_to_test):
            print("\n⏸️ Пауза 5 секунд...")
            await asyncio.sleep(5)
    
    print("\n🎉 Отладка завершена!")


if __name__ == "__main__":
    asyncio.run(main())