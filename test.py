#!/usr/bin/env python3
"""
Улучшенный скрипт для парсинга товаров с Uzum Market
Исправлены проблемы с парсингом футера вместо товаров
"""

import sys
import json
import asyncio
import re
from typing import List, Dict, Optional
from urllib.parse import urljoin, urlparse

from playwright.async_api import async_playwright, Page
from bs4 import BeautifulSoup


class UzumParser:
    def __init__(self, base_url: str = "https://uzum.uz"):
        self.base_url = base_url
        self.products = []

    async def load_page_with_js(self, url: str) -> str:
        """Загружает SPA страницу через Playwright с оптимизированными настройками"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=False,  # Показываем браузер для отладки
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                ignore_https_errors=True
            )
            
            page = await context.new_page()
            
            try:
                print(f"Загружаем страницу: {url}")
                
                # Увеличиваем timeout и меняем стратегию ожидания
                response = await page.goto(url, wait_until="domcontentloaded", timeout=30000)
                
                # Дополнительное ожидание для полной загрузки SPA
                await page.wait_for_timeout(8000)
                
                if response and response.status != 200:
                    print(f"HTTP статус: {response.status}")
                
                # Ждём загрузки основного контента
                await self._wait_for_content(page)
                
                # Скроллим для загрузки товаров
                await self._smart_scroll(page)
                
                html = await page.content()
                print(f"Получен HTML размером: {len(html)} символов")
                return html
                
            except Exception as e:
                print(f"Ошибка при загрузке: {e}")
                # Пробуем получить частично загруженную страницу
                try:
                    html = await page.content()
                    if len(html) > 5000:  # Если есть хоть какой-то контент
                        print("Используем частично загруженную страницу")
                        return html
                except:
                    pass
                raise e
            finally:
                await context.close()
                await browser.close()

    async def _wait_for_content(self, page: Page):
        """Ждёт загрузки контента с товарами"""
        print("Ожидание загрузки контента...")
        
        try:
            # Ждём появления любого из возможных контейнеров товаров
            await page.wait_for_function(
                """
                () => {
                    // Ищем контейнеры с товарами по разным признакам
                    const productLinks = document.querySelectorAll('a[href*="/product/"]');
                    const grids = document.querySelectorAll('[class*="grid"], [class*="Grid"]');
                    const cards = document.querySelectorAll('[class*="card"], [class*="Card"]');

                    return productLinks.length > 3 || grids.length > 0 || cards.length > 5;
                }
                """,
                timeout=20000
            )
            
            # Дополнительное ожидание для полной загрузки
            await page.wait_for_timeout(3000)
            print("✓ Контент загружен")
            
        except Exception as e:
            print(f"⚠ Таймаут загрузки контента: {e}")

    async def _smart_scroll(self, page: Page):
        """Умный скроллинг для загрузки товаров"""
        print("Выполняем скроллинг...")
        
        try:
            # Получаем начальное количество ссылок на товары
            initial_count = await page.evaluate("""
                () => document.querySelectorAll('a[href*="/product/"]').length
            """)
            print(f"Начальное количество товаров: {initial_count}")
            
            # Скроллим по частям с большими паузами
            for i in range(5):
                await page.evaluate(f"""
                    window.scrollTo({{
                        top: {(i + 1) * 800},
                        behavior: 'smooth'
                    }});
                """)
                await page.wait_for_timeout(4000)  # Увеличиваем паузу
                
                current_count = await page.evaluate("""
                    () => document.querySelectorAll('a[href*="/product/"]').length
                """)
                print(f"После скролла {i+1}: {current_count} товаров")
                
                if current_count >= 50:  # Больше товаров
                    break
            
            print("✓ Скроллинг завершён")
            
        except Exception as e:
            print(f"Ошибка при скроллинге: {e}")

    def parse_products_from_spa(self, html: str) -> List[Dict]:
        """Парсит товары с фокусом на реальные товары, исключая футер"""
        soup = BeautifulSoup(html, 'html.parser')
        products = []
        
        print("Анализ структуры страницы...")
        
        # Сначала исключаем футер и другие не товарные блоки
        self._remove_non_product_sections(soup)
        
        # Ищем товары разными способами
        product_containers = self._find_real_products(soup)
        
        print(f"Найдено потенциальных товаров: {len(product_containers)}")
        
        # Отладочная информация
        self._debug_found_elements(product_containers[:5])
        
        processed = 0
        for container in product_containers:
            if processed >= 50:
                break
                
            try:
                product = self._extract_product_from_container(container)
                if product and self._is_valid_product(product):
                    products.append(product)
                    processed += 1
                    print(f"✓ Товар {processed}: {product['name'][:60]}...")
                    
            except Exception as e:
                print(f"Ошибка при обработке товара: {e}")
                continue
        
        return products

    def _remove_non_product_sections(self, soup: BeautifulSoup):
        """Удаляет футер и другие не товарные секции"""
        # Удаляем футер
        for footer in soup.find_all(['footer', '[class*="footer"]', '[class*="Footer"]']):
            footer.decompose()
        
        # Удаляем навигацию
        for nav in soup.find_all(['nav', '[class*="nav"]', '[class*="Nav"]', 'header']):
            nav.decompose()
        
        # Удаляем сайдбары и фильтры
        for sidebar in soup.select('[class*="sidebar"], [class*="filter"], [class*="Filter"]'):
            sidebar.decompose()

    def _find_real_products(self, soup: BeautifulSoup) -> List:
        """Ищет реальные товары, исключая ложные срабатывания"""
        print("Поиск товаров методом 1: По ссылкам на продукты")
        
        # Метод 1: Поиск по ссылкам на товары
        product_links = soup.find_all('a', href=re.compile(r'/product/[\w-]+'))
        if product_links:
            print(f"Найдено {len(product_links)} ссылок на товары")
            
            # Группируем по родительским контейнерам
            containers = []
            seen_containers = set()
            
            for link in product_links:
                # Поднимаемся вверх по DOM для поиска карточки товара
                container = self._find_product_container(link)
                if container and id(container) not in seen_containers:
                    containers.append(container)
                    seen_containers.add(id(container))
            
            if containers:
                return containers[:50]
        
        print("Поиск товаров методом 2: По структуре сетки")
        
        # Метод 2: Поиск по структуре сетки
        grid_selectors = [
            '[class*="grid"] > div',
            '[class*="Grid"] > div',
            '[class*="catalog"] > div',
            '[class*="products"] > div'
        ]
        
        for selector in grid_selectors:
            elements = soup.select(selector)
            # Фильтруем только те, что содержат ссылки на товары
            valid_elements = []
            for elem in elements:
                if elem.find('a', href=re.compile(r'/product/[\w-]+')):
                    valid_elements.append(elem)
            
            if valid_elements:
                print(f"Найдено {len(valid_elements)} элементов по селектору: {selector}")
                return valid_elements[:50]
        
        print("Поиск товаров методом 3: Резервный поиск")
        
        # Метод 3: Резервный поиск
        all_links = soup.find_all('a', href=re.compile(r'/product/[\w-]+'))
        containers = []
        for link in all_links[:50]:
            parent = link.parent
            if parent and len(parent.get_text(strip=True)) > 10:
                containers.append(parent)
        
        return containers

    def _find_product_container(self, link_elem) -> Optional:
        """Находит контейнер карточки товара для ссылки"""
        current = link_elem
        
        # Поднимаемся максимум на 6 уровней
        for level in range(6):
            if current.parent is None:
                break
                
            current = current.parent
            
            # Проверяем, похож ли этот элемент на карточку товара
            if self._looks_like_product_card(current):
                return current
        
        # Если не нашли подходящий контейнер, возвращаем ближайший div
        current = link_elem
        for _ in range(3):
            if current.parent and current.parent.name in ['div', 'article', 'section']:
                return current.parent
            current = current.parent
        
        return link_elem.parent

    def _looks_like_product_card(self, element) -> bool:
        """Проверяет, похож ли элемент на карточку товара"""
        if not element or not hasattr(element, 'get_text'):
            return False
        
        text = element.get_text(strip=True)
        
        # Слишком мало текста
        if len(text) < 20:
            return False
        
        # Слишком много текста (вероятно, это большой блок)
        if len(text) > 1000:
            return False
        
        # Проверяем наличие признаков товара
        has_price = bool(re.search(r'\d+\s*(сум|руб)', text, re.IGNORECASE))
        has_product_link = bool(element.find('a', href=re.compile(r'/product/[\w-]+')))
        has_image = bool(element.find('img'))
        
        return has_product_link and (has_price or has_image)

    def _debug_found_elements(self, elements: List):
        """Отладочная информация о найденных элементах"""
        print("\n🔍 ОТЛАДКА: Анализ найденных элементов")
        print("-" * 50)
        
        for i, elem in enumerate(elements, 1):
            text = elem.get_text(strip=True)[:100]
            has_link = bool(elem.find('a', href=re.compile(r'/product/[\w-]+')))
            has_img = bool(elem.find('img'))
            has_price = bool(re.search(r'\d+\s*(сум|руб)', text, re.IGNORECASE))
            
            print(f"Элемент {i}:")
            print(f"  Текст: {text}...")
            print(f"  Ссылка: {'✓' if has_link else '✗'}")
            print(f"  Изображение: {'✓' if has_img else '✗'}")
            print(f"  Цена: {'✓' if has_price else '✗'}")
            print()

    def _extract_product_from_container(self, container) -> Optional[Dict]:
        """Извлекает данные товара из контейнера"""
        product = {}
        
        # Название
        name = self._extract_product_name(container)
        if not name:
            return None
        product['name'] = name
        
        # Цена
        product['price'] = self._extract_product_price(container)
        
        # URL
        product['url'] = self._extract_product_url(container)
        
        # Вес
        product['weight'] = self._extract_weight_from_text(container.get_text())
        
        # Отзывы
        product['reviews_count'] = self._extract_reviews_count(container)
        
        # Изображение
        product['image'] = self._extract_product_image(container)
        
        return product

    def _extract_product_name(self, container) -> Optional[str]:
        """Извлекает название товара"""
        # Сначала пробуем извлечь из ссылки
        link = container.find('a', href=re.compile(r'/product/[\w-]+'))
        if link:
            # Из атрибута title
            if link.get('title') and len(link.get('title').strip()) > 5:
                return link.get('title').strip()
            
            # Из текста ссылки
            link_text = link.get_text(strip=True)
            if link_text and len(link_text) > 5 and 'сум' not in link_text.lower():
                return link_text
        
        # Ищем в заголовках
        for tag in ['h1', 'h2', 'h3', 'h4', 'h5']:
            heading = container.find(tag)
            if heading:
                text = heading.get_text(strip=True)
                if text and len(text) > 5:
                    return text
        
        # Ищем самый длинный осмысленный текст
        all_texts = []
        for elem in container.find_all(string=True):
            text = elem.strip()
            if (len(text) > 10 and 
                not re.match(r'^\d+\s*(сум|руб)', text, re.IGNORECASE) and
                'отзыв' not in text.lower()):
                all_texts.append(text)
        
        if all_texts:
            # Возвращаем самый длинный текст
            return max(all_texts, key=len)
        
        return None

    def _extract_product_price(self, container) -> Optional[int]:
        """Извлекает цену товара"""
        text = container.get_text()
        
        # Паттерны для поиска цены
        price_patterns = [
            r'(\d{1,3}(?:\s\d{3})*)\s*сум',
            r'(\d{1,3}(?:,\d{3})*)\s*сум',
            r'(\d+)\s*сум'
        ]
        
        for pattern in price_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                # Берём максимальную цену (основную, не скидочную)
                prices = []
                for match in matches:
                    clean_price = match.replace(' ', '').replace(',', '')
                    try:
                        prices.append(int(clean_price))
                    except ValueError:
                        continue
                
                if prices:
                    return max(prices)  # Возвращаем наибольшую (обычную) цену
        
        return None

    def _extract_product_url(self, container) -> Optional[str]:
        """Извлекает URL товара"""
        link = container.find('a', href=re.compile(r'/product/[\w-]+'))
        if link:
            href = link.get('href')
            if href:
                if href.startswith('http'):
                    return href
                else:
                    return urljoin(self.base_url, href)
        return None

    def _extract_weight_from_text(self, text: str) -> Optional[str]:
        """Извлекает вес из текста"""
        weight_patterns = [
            r'(\d+(?:\.\d+)?\s*кг)',
            r'(\d+(?:\.\d+)?\s*г)',
            r'(\d+(?:\.\d+)?\s*мл)',
            r'(\d+(?:\.\d+)?\s*л)',
            r'(\d+(?:\.\d+)?\s*kg)'
        ]
        
        for pattern in weight_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        return None

    def _extract_reviews_count(self, container) -> int:
        """Извлекает количество отзывов"""
        text = container.get_text()
        
        review_patterns = [
            r'(\d+)\s*отзыв',
            r'(\d+)\s*оценк',
            r'отзывов:\s*(\d+)',
            r'\((\d+)\)'  # Часто отзывы в скобках
        ]
        
        for pattern in review_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    return int(match.group(1))
                except ValueError:
                    continue
        
        return 0

    def _extract_product_image(self, container) -> Optional[str]:
        """Извлекает URL изображения товара"""
        # Ищем изображения
        img = container.find('img')
        if img:
            # Проверяем различные атрибуты
            for attr in ['src', 'data-src', 'data-lazy-src']:
                src = img.get(attr)
                if src:
                    if src.startswith('http'):
                        return src
                    elif src.startswith('//'):
                        return 'https:' + src
                    elif src.startswith('/'):
                        return urljoin(self.base_url, src)
        
        return None

    def _is_valid_product(self, product: Dict) -> bool:
        """Проверяет валидность товара"""
        # Должно быть название
        if not product.get('name') or len(product['name'].strip()) < 5:
            return False
        
        # Название не должно быть слишком коротким или подозрительным
        name = product['name'].lower()
        suspicious_words = ['реклама', 'баннер', 'footer', 'навигация']
        if any(word in name for word in suspicious_words):
            return False
        
        # Желательно наличие цены или URL
        has_price = product.get('price') and product['price'] > 0
        has_url = product.get('url') and '/product/' in product['url']
        
        return has_price or has_url

    def save_to_json(self, products: List[Dict], filename: str = "uzum_data.json"):
        """Сохраняет данные в JSON файл"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(products, f, ensure_ascii=False, indent=2)
            
            print(f"\n✅ Данные сохранены в файл: {filename}")
            print(f"📊 Всего товаров: {len(products)}")
            
            # Детальная статистика
            with_prices = sum(1 for p in products if p.get('price'))
            with_urls = sum(1 for p in products if p.get('url'))
            with_images = sum(1 for p in products if p.get('image'))
            with_reviews = sum(1 for p in products if p.get('reviews_count', 0) > 0)
            with_weight = sum(1 for p in products if p.get('weight'))
            
            print(f"💰 С ценами: {with_prices} ({with_prices/len(products)*100:.1f}%)")
            print(f"🔗 С URL: {with_urls} ({with_urls/len(products)*100:.1f}%)")
            print(f"🖼 С изображениями: {with_images} ({with_images/len(products)*100:.1f}%)")
            print(f"⭐ С отзывами: {with_reviews} ({with_reviews/len(products)*100:.1f}%)")
            print(f"⚖️ С весом: {with_weight} ({with_weight/len(products)*100:.1f}%)")
            
        except Exception as e:
            print(f"❌ Ошибка при сохранении: {e}")

    async def parse_category(self, category_url: str):
        """Основной метод парсинга"""
        try:
            print("🚀 Начинаем улучшенный парсинг Uzum Market")
            print("=" * 60)
            
            html = await self.load_page_with_js(category_url)
            
            if not html or len(html) < 5000:
                print("❌ Не удалось загрузить страницу корректно")
                return []
            
            products = self.parse_products_from_spa(html)
            
            if products:
                self.save_to_json(products)
                
                # Показываем примеры найденных товаров
                print(f"\n📦 ПРИМЕРЫ НАЙДЕННЫХ ТОВАРОВ:")
                print("-" * 40)
                for i, product in enumerate(products[:3], 1):
                    print(f"{i}. {product['name']}")
                    if product.get('price'):
                        print(f"   💰 {product['price']} сум")
                    if product.get('url'):
                        print(f"   🔗 {product['url']}")
                    print()
                
            else:
                print("❌ Товары не найдены")
                # Сохраняем HTML для отладки
                with open('debug_uzum.html', 'w', encoding='utf-8') as f:
                    f.write(html)
                print("🔍 HTML сохранён в debug_uzum.html")
            
            return products
            
        except Exception as e:
            print(f"💥 Критическая ошибка: {e}")
            return []


async def main():
    if len(sys.argv) != 2:
        print("Использование: python uzum_parser.py <URL_категории>")
        print("\nПримеры:")
        print("python uzum_parser.py 'https://uzum.uz/ru/category/smartfony-15509'")
        print("python uzum_parser.py 'https://uzum.uz/ru/category/noutbuki-15417'")
        sys.exit(1)
    
    category_url = sys.argv[1]
    
    if not category_url.startswith('http'):
        print("❌ URL должен начинаться с http:// или https://")
        sys.exit(1)
    
    parser = UzumParser()
    products = await parser.parse_category(category_url)
    
    if products:
        print(f"\n🎉 Парсинг завершён успешно!")
        print(f"📦 Получено товаров: {len(products)}")
    else:
        print(f"\n😞 Товары не найдены")
        print("💡 Проверьте URL или попробуйте другую категорию")


if __name__ == "__main__":
    asyncio.run(main())