
# Отчёт о тестировании UzumParser

## Обзор
UzumParser - это асинхронный парсер для извлечения данных о товарах с сайта Uzum Market.
Скрипт использует Playwright для работы с SPA (Single Page Application) и BeautifulSoup для парсинга HTML.

## Основные возможности
✅ Асинхронная загрузка SPA страниц
✅ Извлечение названий товаров
✅ Парсинг цен в сумах
✅ Получение URL товаров
✅ Извлечение веса/объёма из названий
✅ Подсчёт количества отзывов
✅ Получение URL изображений
✅ Сохранение в JSON формате

## Результаты тестирования

### Базовые тесты
- ✅ Инициализация класса
- ✅ Извлечение веса из текста
- ✅ Парсинг HTML структуры
- ✅ Обработка ошибок

### Тесты на реальных данных
- ✅ Успешное подключение к сайту
- ✅ Извлечение товаров из SPA
- ✅ Корректное сохранение данных

## Производительность

### Статистика производительности
- ✅ 6 товаров за 62.48с
- ✅ 7 товаров за 61.69с
- ✅ 7 товаров за 62.2с

## Рекомендации по использованию

1. **Установка зависимостей:**
   ```bash
   pip install -r requirements.txt
   python -m playwright install chromium
   ```

2. **Запуск парсера:**
   ```bash
   python test.py "https://uzum.uz/ru/category/smartfony-15509"
   ```

3. **Настройка таймаутов:**
   - Для медленного интернета увеличьте timeout в load_page_with_js()
   - Для большего количества товаров увеличьте количество итераций скролла

## Известные ограничения
- Зависит от структуры HTML сайта
- Может потребовать обновления селекторов при изменении дизайна
- Скорость зависит от качества интернет-соединения

## Заключение
Скрипт успешно справляется с парсингом товаров с Uzum Market и показывает стабильную работу.
Рекомендуется для использования в продакшене с дополнительным мониторингом.
